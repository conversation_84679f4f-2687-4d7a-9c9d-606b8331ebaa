# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
API_RELOAD=false

# Security
SECRET_KEY=your-super-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database
DATABASE_URL=postgresql://phishing_user:phishing_pass@localhost:5432/phishing_db

# Redis Cache
REDIS_URL=redis://localhost:6379

# ML Models
MODEL_PATH=./models
XGBOOST_MODEL_PATH=./XGBoostClassifier.pickle.dat

# Email Configuration (Optional)
IMAP_SERVER=imap.gmail.com
IMAP_PORT=993
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password

# Feature Extraction
MAX_URL_LENGTH=2048
REQUEST_TIMEOUT=10
USER_AGENT=PhishingDetector/1.0

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Performance
MAX_CONCURRENT_REQUESTS=100
BATCH_SIZE=32
