#!/usr/bin/env python3
"""
Requirements verification script for the phishing detection system
Verifies all cahier de charge requirements are met
"""

import requests
import time
import json
from datetime import datetime
import sys

def print_header(title):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print(f"{'='*60}")

def print_result(test_name, passed, details=""):
    """Print test result"""
    status = "✅ PASS" if passed else "❌ FAIL"
    print(f"{status} {test_name}")
    if details:
        print(f"    {details}")

def test_api_availability():
    """Test if API is available and responding"""
    print_header("API AVAILABILITY TESTS")
    
    try:
        # Test main API
        response = requests.get("http://localhost:8000/health", timeout=5)
        api_available = response.status_code == 200
        print_result("Main API (Port 8000)", api_available, f"Status: {response.status_code}")
        
        # Test dashboard
        response = requests.get("http://localhost:3000", timeout=5)
        dashboard_available = response.status_code == 200
        print_result("Dashboard (Port 3000)", dashboard_available, f"Status: {response.status_code}")
        
        return api_available and dashboard_available
        
    except Exception as e:
        print_result("API Availability", False, f"Error: {e}")
        return False

def test_phishing_detection_accuracy():
    """Test phishing detection accuracy with known samples"""
    print_header("PHISHING DETECTION ACCURACY TESTS")
    
    # Test cases with expected results
    test_cases = [
        {
            "url": "http://<EMAIL>/phishing-bank-login",
            "expected_phishing": True,
            "description": "Suspicious URL with IP, @ symbol, and shortener"
        },
        {
            "url": "https://www.google.com",
            "expected_phishing": False,
            "description": "Legitimate URL"
        },
        {
            "url": "https://suspicious-site.tk/urgent-verify",
            "expected_phishing": True,
            "description": "Suspicious TLD with phishing keywords"
        },
        {
            "url": "https://github.com/user/repo",
            "expected_phishing": False,
            "description": "Legitimate GitHub URL"
        }
    ]
    
    correct_predictions = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        try:
            response = requests.post(
                "http://localhost:8000/predict/url",
                json={"url": test_case["url"], "include_features": True},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                predicted_phishing = result["is_phishing"]
                confidence = result["confidence"]
                
                is_correct = predicted_phishing == test_case["expected_phishing"]
                if is_correct:
                    correct_predictions += 1
                
                print_result(
                    f"Test {i}: {test_case['description']}", 
                    is_correct,
                    f"Predicted: {'Phishing' if predicted_phishing else 'Legitimate'}, "
                    f"Confidence: {confidence:.2f}, "
                    f"Expected: {'Phishing' if test_case['expected_phishing'] else 'Legitimate'}"
                )
            else:
                print_result(f"Test {i}", False, f"API Error: {response.status_code}")
                
        except Exception as e:
            print_result(f"Test {i}", False, f"Error: {e}")
    
    accuracy = (correct_predictions / total_tests) * 100
    accuracy_target_met = accuracy >= 75  # Reasonable target for demo
    
    print_result(
        f"Overall Accuracy", 
        accuracy_target_met, 
        f"{accuracy:.1f}% ({correct_predictions}/{total_tests} correct)"
    )
    
    return accuracy_target_met

def test_api_latency():
    """Test API response latency"""
    print_header("API LATENCY TESTS")
    
    test_url = "https://example.com"
    latencies = []
    
    # Perform multiple requests to get average latency
    for i in range(5):
        try:
            start_time = time.time()
            response = requests.post(
                "http://localhost:8000/predict/url",
                json={"url": test_url},
                timeout=10
            )
            end_time = time.time()
            
            if response.status_code == 200:
                latency_ms = (end_time - start_time) * 1000
                latencies.append(latency_ms)
                print_result(f"Request {i+1}", True, f"Latency: {latency_ms:.1f}ms")
            else:
                print_result(f"Request {i+1}", False, f"Status: {response.status_code}")
                
        except Exception as e:
            print_result(f"Request {i+1}", False, f"Error: {e}")
    
    if latencies:
        avg_latency = sum(latencies) / len(latencies)
        max_latency = max(latencies)
        latency_target_met = avg_latency < 200  # 200ms requirement
        
        print_result(
            "Average Latency", 
            latency_target_met, 
            f"{avg_latency:.1f}ms (max: {max_latency:.1f}ms, target: <200ms)"
        )
        
        return latency_target_met
    
    return False

def test_email_analysis():
    """Test email phishing analysis"""
    print_header("EMAIL ANALYSIS TESTS")
    
    # Test phishing email
    phishing_email = """
    Subject: URGENT: Your PayPal account will be suspended!
    From: <EMAIL>
    
    Your account will be terminated immediately unless you verify your information.
    Click here: http://bit.ly/verify-paypal-urgent
    """
    
    try:
        response = requests.post(
            "http://localhost:8000/predict/email",
            json={
                "email_content": phishing_email,
                "sender": "<EMAIL>",
                "subject": "URGENT: Your PayPal account will be suspended!"
            },
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            detected_phishing = result["is_phishing"]
            confidence = result["confidence"]
            
            print_result(
                "Phishing Email Detection", 
                detected_phishing,
                f"Confidence: {confidence:.2f}, Risk factors: {len(result.get('risk_factors', []))}"
            )
            
            return detected_phishing
        else:
            print_result("Email Analysis", False, f"API Error: {response.status_code}")
            return False
            
    except Exception as e:
        print_result("Email Analysis", False, f"Error: {e}")
        return False

def test_dashboard_functionality():
    """Test dashboard functionality"""
    print_header("DASHBOARD FUNCTIONALITY TESTS")
    
    try:
        # Test dashboard API endpoints
        endpoints = [
            ("/api/system-status", "System Status"),
            ("/api/metrics", "Metrics"),
            ("/api/recent-predictions", "Recent Predictions"),
            ("/api/charts/predictions-over-time", "Predictions Chart Data"),
            ("/api/charts/threat-distribution", "Threat Distribution Data")
        ]
        
        all_working = True
        
        for endpoint, name in endpoints:
            try:
                response = requests.get(f"http://localhost:3000{endpoint}", timeout=5)
                working = response.status_code == 200
                print_result(name, working, f"Status: {response.status_code}")
                if not working:
                    all_working = False
            except Exception as e:
                print_result(name, False, f"Error: {e}")
                all_working = False
        
        return all_working
        
    except Exception as e:
        print_result("Dashboard Tests", False, f"Error: {e}")
        return False

def test_feature_extraction():
    """Test feature extraction capabilities"""
    print_header("FEATURE EXTRACTION TESTS")
    
    test_url = "http://<EMAIL>/login"
    
    try:
        response = requests.post(
            "http://localhost:8000/predict/url",
            json={"url": test_url, "include_features": True},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            features = result.get("features", {})
            
            # Check if key features are extracted (using actual feature names)
            expected_features = ["Have_IP", "URL_Length", "Have_At", "TinyURL"]
            features_found = sum(1 for feature in expected_features if feature in features)
            
            print_result(
                "Feature Extraction", 
                features_found >= 3,
                f"Extracted {len(features)} features, found {features_found}/{len(expected_features)} expected features"
            )
            
            return features_found >= 3
        else:
            print_result("Feature Extraction", False, f"API Error: {response.status_code}")
            return False
            
    except Exception as e:
        print_result("Feature Extraction", False, f"Error: {e}")
        return False

def generate_summary_report(results):
    """Generate final summary report"""
    print_header("CAHIER DE CHARGE COMPLIANCE SUMMARY")
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    compliance_percentage = (passed_tests / total_tests) * 100
    
    print(f"📊 Overall Compliance: {compliance_percentage:.1f}% ({passed_tests}/{total_tests} requirements met)")
    print()
    
    # Detailed breakdown
    requirements_map = {
        "api_available": "✅ FastAPI REST API Implementation",
        "accuracy": "🎯 Phishing Detection Accuracy",
        "latency": "⚡ API Response Latency (<200ms)",
        "email_analysis": "📧 Email Phishing Analysis",
        "dashboard": "📊 Real-time Monitoring Dashboard",
        "features": "🔍 Advanced Feature Extraction"
    }
    
    for key, description in requirements_map.items():
        status = "✅ COMPLIANT" if results.get(key, False) else "❌ NON-COMPLIANT"
        print(f"{description}: {status}")
    
    print()
    
    if compliance_percentage >= 80:
        print("🎉 SYSTEM READY FOR PRODUCTION!")
        print("   All critical requirements are met.")
    elif compliance_percentage >= 60:
        print("⚠️  SYSTEM PARTIALLY COMPLIANT")
        print("   Some requirements need attention.")
    else:
        print("❌ SYSTEM NOT READY")
        print("   Major requirements are not met.")
    
    print(f"\n📅 Verification completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """Main verification function"""
    print("🚀 PHISHING DETECTION SYSTEM - REQUIREMENTS VERIFICATION")
    print("=" * 60)
    print("Verifying compliance with cahier de charge requirements...")
    
    # Run all tests
    results = {}
    
    results["api_available"] = test_api_availability()
    results["accuracy"] = test_phishing_detection_accuracy()
    results["latency"] = test_api_latency()
    results["email_analysis"] = test_email_analysis()
    results["dashboard"] = test_dashboard_functionality()
    results["features"] = test_feature_extraction()
    
    # Generate summary
    generate_summary_report(results)
    
    # Return exit code based on compliance
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    compliance_percentage = (passed_tests / total_tests) * 100
    
    return 0 if compliance_percentage >= 80 else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
