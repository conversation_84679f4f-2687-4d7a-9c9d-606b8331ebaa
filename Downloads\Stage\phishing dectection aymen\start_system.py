#!/usr/bin/env python3
"""
System startup script for the phishing detection system
Handles initialization, training, and deployment
"""

import subprocess
import sys
import argparse
import os
import time
from pathlib import Path
import json

def run_command(command, description, check=True):
    """Run a command with error handling"""
    print(f"\n{'='*60}")
    print(f"Step: {description}")
    print(f"Command: {command}")
    print(f"{'='*60}")
    
    result = subprocess.run(command, shell=True)
    
    if check and result.returncode != 0:
        print(f"❌ Failed: {description}")
        sys.exit(1)
    elif result.returncode == 0:
        print(f"✅ Success: {description}")
    
    return result.returncode == 0

def check_requirements():
    """Check system requirements"""
    print("🔍 Checking system requirements...")
    
    # Check Python version
    if sys.version_info < (3, 10):
        print("❌ Python 3.10+ is required")
        sys.exit(1)
    
    # Check if Docker is available
    docker_available = subprocess.run("docker --version", shell=True, capture_output=True).returncode == 0
    docker_compose_available = subprocess.run("docker-compose --version", shell=True, capture_output=True).returncode == 0
    
    print(f"✅ Python {sys.version}")
    print(f"{'✅' if docker_available else '❌'} Docker {'available' if docker_available else 'not available'}")
    print(f"{'✅' if docker_compose_available else '❌'} Docker Compose {'available' if docker_compose_available else 'not available'}")
    
    return docker_available and docker_compose_available

def setup_environment():
    """Set up environment and configuration"""
    print("🔧 Setting up environment...")
    
    # Create necessary directories
    directories = ["models", "logs", "DataFiles", "monitoring"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"📁 Created directory: {directory}")
    
    # Copy environment file if it doesn't exist
    if not Path(".env").exists():
        if Path(".env.example").exists():
            run_command("cp .env.example .env", "Copy environment template")
            print("⚠️  Please edit .env file with your configuration")
        else:
            print("⚠️  No .env.example found, creating basic .env")
            with open(".env", "w") as f:
                f.write("SECRET_KEY=change-this-in-production\n")
                f.write("DATABASE_URL=postgresql://phishing_user:phishing_pass@localhost:5432/phishing_db\n")
                f.write("LOG_LEVEL=INFO\n")

def install_dependencies():
    """Install Python dependencies"""
    print("📦 Installing dependencies...")
    
    # Upgrade pip
    run_command("python -m pip install --upgrade pip", "Upgrade pip")
    
    # Install requirements
    if Path("requirements.txt").exists():
        run_command("pip install -r requirements.txt", "Install Python dependencies")
    else:
        print("⚠️  No requirements.txt found")

def train_models(args):
    """Train ML models"""
    if args.skip_training:
        print("⏭️  Skipping model training")
        return
    
    print("🤖 Training ML models...")
    
    # Check if models already exist
    model_files = list(Path("models").glob("*.pkl"))
    if model_files and not args.retrain:
        print(f"✅ Found existing models: {[f.name for f in model_files]}")
        print("Use --retrain to force retraining")
        return
    
    # Run training script
    train_cmd = "python real_model_trainer.py"
    
    if args.target_f1:
        train_cmd += f" --target-f1 {args.target_f1}"
    
    if args.use_ensemble:
        train_cmd += " --use-ensemble"
    
    if args.optimize_hyperparams:
        train_cmd += " --optimize-hyperparams"
    
    run_command(train_cmd, "Train ML models")

def run_tests():
    """Run test suite"""
    print("🧪 Running tests...")
    
    # Run verification instead of old tests
    run_command("python verify_requirements.py", "Run system verification", check=False)

def start_services(args):
    """Start system services"""
    print("🚀 Starting system services...")
    
    if args.docker:
        # Start with Docker Compose
        compose_file = "docker-compose.yml"
        if args.production:
            compose_file = "docker-compose.prod.yml"
        
        if Path(compose_file).exists():
            run_command(f"docker-compose -f {compose_file} up -d", f"Start services with {compose_file}")
            
            # Wait for services to be ready
            print("⏳ Waiting for services to be ready...")
            time.sleep(30)
            
            # Check service health
            run_command("docker-compose ps", "Check service status", check=False)
            
        else:
            print(f"❌ {compose_file} not found")
            sys.exit(1)
    
    else:
        # Start API directly
        api_cmd = "python -m uvicorn real_api:app --host 0.0.0.0 --port 8000"
        
        if args.reload:
            api_cmd += " --reload"
        
        if args.workers:
            api_cmd += f" --workers {args.workers}"
        
        print(f"Starting API: {api_cmd}")
        print("Press Ctrl+C to stop")
        
        try:
            subprocess.run(api_cmd, shell=True)
        except KeyboardInterrupt:
            print("\n🛑 Stopping API...")

def show_status():
    """Show system status"""
    print("📊 System Status:")
    
    # Check if services are running
    services = [
        ("API", "http://localhost:8000/health"),
        ("Dashboard", "http://localhost:3000"),
        ("Prometheus", "http://localhost:9090"),
        ("Grafana", "http://localhost:3000")
    ]
    
    for service, url in services:
        try:
            import requests
            response = requests.get(url, timeout=5)
            status = "✅ Running" if response.status_code == 200 else "❌ Error"
        except:
            status = "❌ Not accessible"
        
        print(f"  {service}: {status} ({url})")

def main():
    parser = argparse.ArgumentParser(description='Start phishing detection system')
    
    # Training options
    parser.add_argument('--skip-training', action='store_true', help='Skip model training')
    parser.add_argument('--retrain', action='store_true', help='Force model retraining')
    parser.add_argument('--target-f1', type=float, default=0.95, help='Target F1 score')
    parser.add_argument('--use-ensemble', action='store_true', help='Use ensemble methods')
    parser.add_argument('--optimize-hyperparams', action='store_true', help='Optimize hyperparameters')
    
    # Deployment options
    parser.add_argument('--docker', action='store_true', help='Use Docker deployment')
    parser.add_argument('--production', action='store_true', help='Use production configuration')
    parser.add_argument('--reload', action='store_true', help='Enable auto-reload (development)')
    parser.add_argument('--workers', type=int, help='Number of API workers')
    
    # Other options
    parser.add_argument('--skip-tests', action='store_true', help='Skip running tests')
    parser.add_argument('--status-only', action='store_true', help='Show status only')
    
    args = parser.parse_args()
    
    print("🔥 Phishing Detection System Startup")
    print("=" * 60)
    
    if args.status_only:
        show_status()
        return
    
    # Check requirements
    docker_available = check_requirements()
    
    if args.docker and not docker_available:
        print("❌ Docker is required for Docker deployment")
        sys.exit(1)
    
    # Setup environment
    setup_environment()
    
    # Install dependencies (if not using Docker)
    if not args.docker:
        install_dependencies()
    
    # Train models
    train_models(args)
    
    # Run tests
    if not args.skip_tests:
        run_tests()
    
    # Start services
    start_services(args)
    
    # Show final status
    if args.docker:
        print("\n🎉 System started successfully!")
        print("\nAccess points:")
        print("  API: http://localhost:8000")
        print("  Dashboard: http://localhost:3000")
        print("  API Docs: http://localhost:8000/docs")
        print("  Prometheus: http://localhost:9090")
        print("\nTo stop the system: docker-compose down")

if __name__ == "__main__":
    main()
