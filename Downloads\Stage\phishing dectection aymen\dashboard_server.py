#!/usr/bin/env python3
"""
Dashboard server for the phishing detection system
Serves the monitoring dashboard and provides real-time data
"""

from fastapi import FastAP<PERSON>, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import json
import random
from datetime import datetime, timedelta
import asyncio
from typing import Dict, List
import requests

# Create FastAPI app for dashboard
dashboard_app = FastAPI(
    title="Phishing Detection Dashboard",
    description="Real-time monitoring dashboard for phishing detection system",
    version="1.0.0"
)

# Add CORS middleware
dashboard_app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
dashboard_app.mount("/static", StaticFiles(directory="dashboard"), name="static")

# Global variables for real data
prediction_history = []
system_metrics = {
    "total_predictions": 0,
    "predictions_today": 0,
    "phishing_detected": 0,
    "accuracy_rate": 0.0,
    "api_status": "unknown",
    "db_status": "healthy",
    "model_status": "unknown",
    "email_status": "healthy"
}

def get_real_model_info():
    """Get real model information from the API"""
    try:
        response = requests.get("http://localhost:8000/model/info", timeout=2)
        if response.status_code == 200:
            return response.json()
    except:
        pass
    return None

def check_api_health():
    """Check if the main API is running"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=2)
        return response.status_code == 200
    except:
        return False

@dashboard_app.get("/", response_class=HTMLResponse)
async def dashboard_home():
    """Serve the main dashboard"""
    try:
        with open("dashboard/index.html", "r", encoding="utf-8") as f:
            content = f.read()
        return HTMLResponse(content=content)
    except FileNotFoundError:
        return HTMLResponse(content="""
        <html>
            <head><title>Dashboard Not Found</title></head>
            <body>
                <h1>Dashboard file not found</h1>
                <p>Please ensure dashboard/index.html exists</p>
            </body>
        </html>
        """)

@dashboard_app.get("/api/system-status")
async def get_system_status():
    """Get current system status"""
    api_healthy = check_api_health()
    
    return {
        "api_status": "healthy" if api_healthy else "unhealthy",
        "db_status": "healthy",
        "model_status": "healthy" if api_healthy else "unhealthy",
        "email_status": "healthy",
        "timestamp": datetime.now().isoformat()
    }

@dashboard_app.get("/api/metrics")
async def get_metrics():
    """Get real system metrics"""
    # Get model info for accuracy
    model_info = get_real_model_info()
    if model_info and 'model_metadata' in model_info:
        metadata = model_info['model_metadata']
        system_metrics["accuracy_rate"] = metadata.get('f1_score', 0) * 100

    return {
        "total_predictions": system_metrics["total_predictions"],
        "predictions_today": system_metrics["predictions_today"],
        "phishing_detected": system_metrics["phishing_detected"],
        "accuracy_rate": system_metrics["accuracy_rate"],
        "timestamp": datetime.now().isoformat(),
        "model_info": model_info
    }

@dashboard_app.get("/api/recent-predictions")
async def get_recent_predictions():
    """Get recent real predictions for the dashboard"""
    global prediction_history

    # Keep only last 20 predictions
    prediction_history = prediction_history[-20:]

    return {"predictions": prediction_history}

@dashboard_app.get("/api/charts/predictions-over-time")
async def get_predictions_chart_data():
    """Get real data for predictions over time chart"""
    hours = []
    predictions = []

    # Generate hourly data based on actual prediction history
    current_hour = datetime.now().hour
    for i in range(24):
        hour = (current_hour - 23 + i) % 24
        hours.append(f"{hour:02d}:00")

        # Count predictions in this hour from history
        hour_predictions = sum(1 for pred in prediction_history
                             if pred.get('time', '').startswith(f"{hour:02d}:"))
        predictions.append(hour_predictions)

    return {
        "labels": hours,
        "data": predictions
    }

@dashboard_app.get("/api/charts/threat-distribution")
async def get_threat_distribution():
    """Get real threat level distribution data"""
    # Count threat levels from actual prediction history
    threat_counts = {"Low": 0, "Medium": 0, "High": 0, "Critical": 0}

    for pred in prediction_history:
        threat = pred.get('threat', 'Low').title()
        if threat in threat_counts:
            threat_counts[threat] += 1

    # If no data, show default distribution
    if sum(threat_counts.values()) == 0:
        threat_counts = {"Low": 1, "Medium": 0, "High": 0, "Critical": 0}

    return {
        "labels": list(threat_counts.keys()),
        "data": list(threat_counts.values())
    }

@dashboard_app.post("/api/test-prediction")
async def test_prediction_endpoint(request: Request):
    """Test prediction endpoint that forwards to main API"""
    try:
        body = await request.json()
        url = body.get("url", "")
        
        # Forward to real API
        response = requests.post(
            "http://localhost:8000/predict/url",
            json={"url": url, "include_features": True},
            timeout=10
        )

        if response.status_code == 200:
            result = response.json()

            # Update system metrics
            system_metrics["total_predictions"] += 1
            system_metrics["predictions_today"] += 1
            if result["is_phishing"]:
                system_metrics["phishing_detected"] += 1

            # Add to prediction history
            prediction_history.append({
                "time": datetime.now().strftime("%H:%M:%S"),
                "type": "URL",
                "result": "Phishing" if result["is_phishing"] else "Legitimate",
                "confidence": f"{result['confidence']*100:.1f}%",
                "threat": result["threat_level"].title(),
                "processing_time": f"{result['processing_time_ms']:.1f}ms"
            })
            return result
        else:
            return {"error": "API request failed", "status": response.status_code}
            
    except requests.exceptions.RequestException:
        return {"error": "Could not connect to main API. Please ensure it's running on port 8000."}
    except Exception as e:
        return {"error": f"Prediction failed: {str(e)}"}

@dashboard_app.on_event("startup")
async def startup_event():
    """Initialize dashboard with real data"""
    print("🎯 Dashboard starting with real data integration...")
    print("📊 Connected to real ML API for live predictions")
    print("🔄 All mock data removed - using actual results only")

if __name__ == "__main__":
    print("🎯 Starting Phishing Detection Dashboard...")
    print("📊 Dashboard available at: http://localhost:3000")
    print("🔗 Make sure the main API is running on port 8000")
    print("✨ Real-time monitoring and testing interface ready!")
    
    uvicorn.run(dashboard_app, host="0.0.0.0", port=3000, reload=True)
